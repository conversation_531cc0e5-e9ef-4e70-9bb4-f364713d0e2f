<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <!-- 基础信息 -->
      <div class="mb-6">
        <h4 class="mb-3 text-base font-medium text-gray-900">基础信息</h4>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="文件标题" name="title">
              <a-input
                v-model:value="formData.title"
                placeholder="请输入文件标题"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文件类型">
              <a-input :value="getFileTypeLabel()" disabled />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="文件描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入文件描述"
            :rows="3"
          />
        </a-form-item>
      </div>

      <!-- 地理坐标（主要用于音频文件） -->
      <div v-if="mediaFile?.type === 'audio'" class="mb-6">
        <h4 class="mb-3 text-base font-medium text-gray-900">地理坐标</h4>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="纬度" name="coordinates.latitude">
              <a-input-number
                v-model:value="formData.coordinates.latitude"
                placeholder="请输入纬度"
                :min="-90"
                :max="90"
                :precision="6"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经度" name="coordinates.longitude">
              <a-input-number
                v-model:value="formData.coordinates.longitude"
                placeholder="请输入经度"
                :min="-180"
                :max="180"
                :precision="6"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 音频元数据 -->
      <div v-if="mediaFile?.type === 'audio'" class="mb-6">
        <h4 class="mb-3 text-base font-medium text-gray-900">音频元数据</h4>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="录音地点" name="metadata.recordingLocation">
              <a-input
                v-model:value="formData.metadata.recordingLocation"
                placeholder="请输入录音地点"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="录音时间" name="metadata.recordingTime">
              <a-date-picker
                v-model:value="recordingTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择录音时间"
                style="width: 100%"
                @change="handleRecordingTimeChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="行为描述" name="metadata.behaviorDescription">
              <a-input
                v-model:value="formData.metadata.behaviorDescription"
                placeholder="如：求偶鸣叫、警戒声等"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="灵敏度" name="metadata.sensitivity">
              <a-select
                v-model:value="formData.metadata.sensitivity"
                placeholder="请选择灵敏度"
              >
                <a-select-option value="低">低</a-select-option>
                <a-select-option value="中">中</a-select-option>
                <a-select-option value="高">高</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="放大倍数" name="metadata.amplification">
          <a-input
            v-model:value="formData.metadata.amplification"
            placeholder="如：1x, 2x, 10x等"
          />
        </a-form-item>
      </div>

      <!-- 文件信息（只读） -->
      <div class="mb-6">
        <h4 class="mb-3 text-base font-medium text-gray-900">文件信息</h4>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="原始文件名">
              <a-input :value="mediaFile?.originalName" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="文件大小">
              <a-input :value="formatFileSize(mediaFile?.size || 0)" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="上传时间">
              <a-input :value="formatDateTime(mediaFile?.createdAt)" disabled />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 音频技术参数 -->
        <div v-if="mediaFile?.type === 'audio' && mediaFile?.metadata">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="时长">
                <a-input :value="formatDuration(mediaFile.metadata.duration)" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="采样率">
                <a-input :value="`${mediaFile.metadata.sampleRate} Hz`" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="位深度">
                <a-input :value="`${mediaFile.metadata.bitDepth} bit`" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="声道数">
                <a-input :value="mediaFile.metadata.channels" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import type { FormInstance, Rule } from 'ant-design-vue/es/form';

import type { MediaFile, MediaMetadataUpdateRequest } from '#/types/species';
import { MediaApi } from '#/api/species';

interface Props {
  open: boolean;
  mediaFile: MediaFile | null;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const loading = ref(false);
const recordingTime = ref<Dayjs>();

// 计算属性
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value),
});

const modalTitle = computed(() => {
  if (!props.mediaFile) return '编辑媒体信息';
  return `编辑${getFileTypeLabel()}信息`;
});

// 表单数据
const formData = reactive<MediaMetadataUpdateRequest>({
  id: '',
  title: '',
  description: '',
  coordinates: {
    latitude: undefined,
    longitude: undefined,
  },
  metadata: {
    recordingLocation: '',
    recordingTime: '',
    behaviorDescription: '',
    sensitivity: '',
    amplification: '',
  },
});

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  title: [
    { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' },
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' },
  ],
  'coordinates.latitude': [
    { type: 'number', min: -90, max: 90, message: '纬度范围应在-90到90之间', trigger: 'blur' },
  ],
  'coordinates.longitude': [
    { type: 'number', min: -180, max: 180, message: '经度范围应在-180到180之间', trigger: 'blur' },
  ],
};

// 监听媒体文件变化，初始化表单数据
watch(
  () => props.mediaFile,
  (mediaFile) => {
    if (mediaFile) {
      formData.id = mediaFile.id;
      formData.title = mediaFile.title || '';
      formData.description = mediaFile.description || '';
      
      if (mediaFile.coordinates) {
        formData.coordinates.latitude = mediaFile.coordinates.latitude;
        formData.coordinates.longitude = mediaFile.coordinates.longitude;
      } else {
        formData.coordinates.latitude = undefined;
        formData.coordinates.longitude = undefined;
      }

      if (mediaFile.metadata) {
        formData.metadata.recordingLocation = mediaFile.metadata.recordingLocation || '';
        formData.metadata.behaviorDescription = mediaFile.metadata.behaviorDescription || '';
        formData.metadata.sensitivity = mediaFile.metadata.sensitivity || '';
        formData.metadata.amplification = mediaFile.metadata.amplification || '';
        
        if (mediaFile.metadata.recordingTime) {
          recordingTime.value = dayjs(mediaFile.metadata.recordingTime);
        } else {
          recordingTime.value = undefined;
        }
      }
    }
  },
  { immediate: true }
);

// 获取文件类型标签
const getFileTypeLabel = (): string => {
  if (!props.mediaFile) return '';
  
  const typeMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
  };
  
  return typeMap[props.mediaFile.type] || '未知';
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化日期时间
const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 处理录音时间变化
const handleRecordingTimeChange = (value: Dayjs | null) => {
  formData.metadata.recordingTime = value ? value.toISOString() : '';
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    // 清理空值
    const updateData: MediaMetadataUpdateRequest = {
      id: formData.id,
      title: formData.title || undefined,
      description: formData.description || undefined,
    };

    // 添加坐标信息（如果有值）
    if (formData.coordinates.latitude !== undefined && formData.coordinates.longitude !== undefined) {
      updateData.coordinates = {
        latitude: formData.coordinates.latitude,
        longitude: formData.coordinates.longitude,
      };
    }

    // 添加音频元数据（如果是音频文件且有值）
    if (props.mediaFile?.type === 'audio') {
      const metadata: any = {};
      
      if (formData.metadata.recordingLocation) {
        metadata.recordingLocation = formData.metadata.recordingLocation;
      }
      if (formData.metadata.recordingTime) {
        metadata.recordingTime = formData.metadata.recordingTime;
      }
      if (formData.metadata.behaviorDescription) {
        metadata.behaviorDescription = formData.metadata.behaviorDescription;
      }
      if (formData.metadata.sensitivity) {
        metadata.sensitivity = formData.metadata.sensitivity;
      }
      if (formData.metadata.amplification) {
        metadata.amplification = formData.metadata.amplification;
      }

      if (Object.keys(metadata).length > 0) {
        updateData.metadata = metadata;
      }
    }

    const response = await MediaApi.updateMediaMetadata(updateData);
    
    if (response.success) {
      message.success('媒体信息更新成功');
      emit('success');
      visible.value = false;
    } else {
      message.error(response.message || '更新失败');
    }
  } catch (error) {
    console.error('更新媒体信息失败:', error);
    message.error('更新媒体信息失败');
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
};
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

h4 {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}
</style>
