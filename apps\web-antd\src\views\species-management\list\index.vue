<template>
  <div class="species-list-container">
    <!-- 页面标题和操作栏 -->
    <div class="mb-6 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">物种管理</h1>
        <p class="mt-1 text-sm text-gray-600">管理和维护物种基础信息、媒体资源和分布数据</p>
      </div>
      <a-button type="primary" @click="handleCreate">
        <template #icon>
          <PlusOutlined />
        </template>
        新增物种
      </a-button>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model:value="searchForm.search"
            placeholder="搜索物种名称或拉丁学名"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="searchForm.conservationStatus"
            placeholder="保护状况"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option
              v-for="status in conservationStatusOptions"
              :key="status.value"
              :value="status.value"
            >
              {{ status.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.category"
            placeholder="分类筛选"
            allow-clear
            @change="handleSearch"
          />
        </a-col>
        <a-col :span="4">
          <a-button @click="handleReset">重置</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 物种列表 -->
    <div class="rounded-lg bg-white shadow-sm">
      <a-table
        :columns="columns"
        :data-source="speciesList"
        :loading="loading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 代表性图片 -->
        <template #representativeImage="{ record }">
          <a-avatar
            v-if="record.representativeImageUrl"
            :src="record.representativeImageUrl"
            shape="square"
            :size="48"
          />
          <a-avatar v-else shape="square" :size="48">
            <template #icon>
              <FileImageOutlined />
            </template>
          </a-avatar>
        </template>

        <!-- 物种名称 -->
        <template #name="{ record }">
          <div>
            <div class="font-medium text-gray-900">{{ record.chineseName }}</div>
            <div class="text-sm text-gray-500">{{ record.englishName }}</div>
            <div class="text-xs italic text-gray-400">{{ record.latinName }}</div>
          </div>
        </template>

        <!-- 分类信息 -->
        <template #taxonomy="{ record }">
          <div class="text-sm">
            <div>{{ record.taxonomy.family }} {{ record.taxonomy.genus }}</div>
            <div class="text-gray-500">{{ record.taxonomy.order }}</div>
          </div>
        </template>

        <!-- 保护状况 -->
        <template #conservationStatus="{ record }">
          <a-tag :color="getStatusColor(record.conservationStatus)">
            {{ getStatusLabel(record.conservationStatus) }}
          </a-tag>
        </template>

        <!-- 媒体统计 -->
        <template #mediaCount="{ record }">
          <div class="flex space-x-2 text-sm">
            <span class="text-blue-600">
              <FileImageOutlined /> {{ record.imageCount || 0 }}
            </span>
            <span class="text-green-600">
              <VideoCameraOutlined /> {{ record.videoCount || 0 }}
            </span>
            <span class="text-purple-600">
              <AudioOutlined /> {{ record.audioCount || 0 }}
            </span>
          </div>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleView(record)">
              查看
            </a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="handleMediaManage(record)">
              媒体管理
            </a-button>
            <a-popconfirm
              title="确定要删除这个物种吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  message,
  Space,
  Table,
  Button,
  Input,
  Select,
  Row,
  Col,
  Avatar,
  Popconfirm
} from 'ant-design-vue';
import {
  AudioOutlined,
  FileImageOutlined,
  PlusOutlined,
  SearchOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons-vue';

import type { TableColumnsType, TableProps } from 'ant-design-vue';
import type { ConservationStatus, Species, SpeciesListQuery } from '#/types/species';

import { SpeciesApi } from '#/api/species';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const speciesList = ref<Species[]>([]);
const total = ref(0);

// 搜索表单
const searchForm = reactive<SpeciesListQuery>({
  page: 1,
  limit: 10,
  search: '',
  category: '',
  conservationStatus: undefined,
  sortBy: 'createdAt',
  sortOrder: 'desc',
});

// 保护状况选项
const conservationStatusOptions = [
  { value: 'LC', label: '无危 (LC)' },
  { value: 'NT', label: '近危 (NT)' },
  { value: 'VU', label: '易危 (VU)' },
  { value: 'EN', label: '濒危 (EN)' },
  { value: 'CR', label: '极危 (CR)' },
  { value: 'EW', label: '野外灭绝 (EW)' },
  { value: 'EX', label: '灭绝 (EX)' },
  { value: 'DD', label: '数据缺乏 (DD)' },
  { value: 'NE', label: '未评估 (NE)' },
];

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '代表图片',
    dataIndex: 'representativeImage',
    key: 'representativeImage',
    width: 80,
    align: 'center',
  },
  {
    title: '物种名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '分类信息',
    dataIndex: 'taxonomy',
    key: 'taxonomy',
    width: 150,
  },
  {
    title: '保护状况',
    dataIndex: 'conservationStatus',
    key: 'conservationStatus',
    width: 120,
    align: 'center',
  },
  {
    title: '媒体统计',
    dataIndex: 'mediaCount',
    key: 'mediaCount',
    width: 120,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 120,
    sorter: true,
    render: (text: string) => new Date(text).toLocaleDateString(),
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
  },
];

// 分页配置
const paginationConfig = computed(() => ({
  current: searchForm.page,
  pageSize: searchForm.limit,
  total: total.value,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
}));

// 获取保护状况颜色
const getStatusColor = (status: ConservationStatus): string => {
  const colorMap: Record<ConservationStatus, string> = {
    LC: 'green',
    NT: 'blue',
    VU: 'orange',
    EN: 'red',
    CR: 'red',
    EW: 'purple',
    EX: 'black',
    DD: 'gray',
    NE: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取保护状况标签
const getStatusLabel = (status: ConservationStatus): string => {
  const option = conservationStatusOptions.find(opt => opt.value === status);
  return option?.label || status;
};

// 加载物种列表
const loadSpeciesList = async () => {
  try {
    loading.value = true;
    const response = await SpeciesApi.getSpeciesList(searchForm);
    
    if (response.success) {
      speciesList.value = response.data;
      total.value = response.pagination.total;
    } else {
      message.error(response.message || '加载物种列表失败');
    }
  } catch (error) {
    console.error('加载物种列表失败:', error);
    message.error('加载物种列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  searchForm.page = 1;
  loadSpeciesList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    limit: 10,
    search: '',
    category: '',
    conservationStatus: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  loadSpeciesList();
};

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (pagination, filters, sorter) => {
  if (pagination) {
    searchForm.page = pagination.current || 1;
    searchForm.limit = pagination.pageSize || 10;
  }
  
  if (sorter && !Array.isArray(sorter)) {
    searchForm.sortBy = sorter.field as any;
    searchForm.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
  }
  
  loadSpeciesList();
};

// 操作处理函数
const handleCreate = () => {
  router.push('/species/create');
};

const handleView = (record: Species) => {
  router.push(`/species/edit/${record.id}?mode=view`);
};

const handleEdit = (record: Species) => {
  router.push(`/species/edit/${record.id}`);
};

const handleMediaManage = (record: Species) => {
  router.push(`/species/${record.id}/media`);
};

const handleDelete = async (record: Species) => {
  try {
    const response = await SpeciesApi.deleteSpecies(record.id);
    if (response.success) {
      message.success('物种删除成功');
      loadSpeciesList();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除物种失败:', error);
    message.error('删除物种失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadSpeciesList();
});
</script>

<style scoped>
.species-list-container {
  min-height: 100vh;
  padding: 24px;
  background-color: #f5f5f5;
}
</style>
