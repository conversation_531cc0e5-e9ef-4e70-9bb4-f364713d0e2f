<template>
  <div class="map-viewer">
    <div class="mb-4 flex items-center justify-between">
      <h4 class="text-base font-medium text-gray-900">地理分布</h4>
      <div class="flex space-x-2">
        <a-button size="small" @click="handleUploadKML">
          <template #icon>
            <UploadOutlined />
          </template>
          上传KML文件
        </a-button>
        <a-button v-if="distribution" size="small" @click="handleFitBounds">
          <template #icon>
            <AimOutlined />
          </template>
          适应边界
        </a-button>
        <a-button v-if="distribution" size="small" danger @click="handleDeleteDistribution">
          <template #icon>
            <DeleteOutlined />
          </template>
          删除分布数据
        </a-button>
      </div>
    </div>

    <!-- 地图容器 -->
    <div
      ref="mapContainer"
      class="map-container"
      :style="{ height: `${height}px` }"
    >
      <div v-if="!mapLoaded" class="flex h-full items-center justify-center bg-gray-100">
        <a-spin size="large" />
        <span class="ml-2 text-gray-600">地图加载中...</span>
      </div>
    </div>

    <!-- 分布信息 -->
    <div v-if="distribution" class="mt-4 rounded-lg bg-gray-50 p-4">
      <h5 class="mb-2 text-sm font-medium text-gray-900">分布信息</h5>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-600">KML文件:</span>
          <span class="ml-1 text-gray-900">{{ distribution.kmlFileName }}</span>
        </div>
        <div>
          <span class="text-gray-600">坐标点数:</span>
          <span class="ml-1 text-gray-900">{{ distribution.coordinates.length }}</span>
        </div>
        <div>
          <span class="text-gray-600">边界范围:</span>
          <span class="ml-1 text-gray-900">
            {{ distribution.boundingBox.north.toFixed(4) }}°N,
            {{ distribution.boundingBox.south.toFixed(4) }}°S,
            {{ distribution.boundingBox.east.toFixed(4) }}°E,
            {{ distribution.boundingBox.west.toFixed(4) }}°W
          </span>
        </div>
        <div>
          <span class="text-gray-600">更新时间:</span>
          <span class="ml-1 text-gray-900">
            {{ new Date(distribution.updatedAt).toLocaleString() }}
          </span>
        </div>
      </div>
    </div>

    <!-- KML上传模态框 -->
    <a-modal
      v-model:open="uploadModalVisible"
      title="上传KML文件"
      :confirm-loading="uploading"
      @ok="handleConfirmUpload"
      @cancel="handleCancelUpload"
    >
      <div class="py-4">
        <a-upload-dragger
          v-model:file-list="kmlFileList"
          name="kmlFile"
          :multiple="false"
          :before-upload="beforeUploadKML"
          accept=".kml"
        >
          <p class="ant-upload-drag-icon">
            <FileOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽KML文件到此区域</p>
          <p class="ant-upload-hint">
            支持标准KML格式文件，用于显示物种地理分布信息
          </p>
        </a-upload-dragger>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  AimOutlined,
  DeleteOutlined,
  FileOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';

import type { UploadFile, UploadProps } from 'ant-design-vue';
import type { GeographicDistribution } from '#/types/species';
import { GeographicApi } from '#/api/species';

interface Props {
  speciesId: string;
  distribution?: GeographicDistribution | null;
  height?: number;
}

interface Emits {
  (e: 'update', distribution: GeographicDistribution): void;
  (e: 'delete'): void;
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
});

const emit = defineEmits<Emits>();

// 响应式数据
const mapContainer = ref<HTMLElement>();
const mapLoaded = ref(false);
const uploadModalVisible = ref(false);
const uploading = ref(false);
const kmlFileList = ref<UploadFile[]>([]);

// 地图实例（这里使用模拟的地图对象）
let mapInstance: any = null;

// 初始化地图
const initMap = async () => {
  try {
    await nextTick();
    
    if (!mapContainer.value) return;

    // 模拟地图初始化
    // 在实际项目中，这里应该使用真实的地图API，如高德地图、百度地图等
    setTimeout(() => {
      mapInstance = {
        // 模拟地图方法
        addMarkers: (coordinates: any[]) => {
          console.log('添加标记点:', coordinates);
        },
        fitBounds: (bounds: any) => {
          console.log('适应边界:', bounds);
        },
        clearMarkers: () => {
          console.log('清除标记点');
        },
        destroy: () => {
          console.log('销毁地图');
        },
      };
      
      mapLoaded.value = true;
      
      // 如果有分布数据，显示在地图上
      if (props.distribution) {
        displayDistribution(props.distribution);
      }
    }, 1000);
  } catch (error) {
    console.error('地图初始化失败:', error);
    message.error('地图初始化失败');
  }
};

// 显示分布数据
const displayDistribution = (distribution: GeographicDistribution) => {
  if (!mapInstance) return;

  // 清除现有标记
  mapInstance.clearMarkers();
  
  // 添加新标记
  mapInstance.addMarkers(distribution.coordinates);
  
  // 适应边界
  mapInstance.fitBounds(distribution.boundingBox);
};

// 监听分布数据变化
watch(
  () => props.distribution,
  (newDistribution) => {
    if (newDistribution && mapInstance) {
      displayDistribution(newDistribution);
    }
  }
);

// 上传KML文件
const handleUploadKML = () => {
  uploadModalVisible.value = true;
  kmlFileList.value = [];
};

// KML文件上传前检查
const beforeUploadKML: UploadProps['beforeUpload'] = (file) => {
  const isKML = file.name.toLowerCase().endsWith('.kml');
  if (!isKML) {
    message.error('只能上传KML格式文件！');
    return false;
  }

  const isLt10M = file.size! / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('KML文件大小不能超过10MB！');
    return false;
  }

  return false; // 阻止自动上传
};

// 确认上传KML
const handleConfirmUpload = async () => {
  if (kmlFileList.value.length === 0) {
    message.error('请选择KML文件');
    return;
  }

  try {
    uploading.value = true;
    
    const file = kmlFileList.value[0].originFileObj as File;
    const response = await GeographicApi.uploadKML({
      speciesId: props.speciesId,
      file,
    });

    if (response.success) {
      message.success('KML文件上传成功');
      emit('update', response.data);
      uploadModalVisible.value = false;
      kmlFileList.value = [];
    } else {
      message.error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('上传KML文件失败:', error);
    message.error('上传KML文件失败');
  } finally {
    uploading.value = false;
  }
};

// 取消上传
const handleCancelUpload = () => {
  uploadModalVisible.value = false;
  kmlFileList.value = [];
};

// 适应边界
const handleFitBounds = () => {
  if (props.distribution && mapInstance) {
    mapInstance.fitBounds(props.distribution.boundingBox);
  }
};

// 删除分布数据
const handleDeleteDistribution = async () => {
  if (!props.distribution) return;

  try {
    const response = await GeographicApi.deleteDistribution(props.distribution.id);
    if (response.success) {
      message.success('分布数据删除成功');
      emit('delete');
      
      // 清除地图标记
      if (mapInstance) {
        mapInstance.clearMarkers();
      }
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除分布数据失败:', error);
    message.error('删除分布数据失败');
  }
};

// 组件挂载时初始化地图
onMounted(() => {
  initMap();
});

// 组件卸载时销毁地图
onUnmounted(() => {
  if (mapInstance) {
    mapInstance.destroy();
    mapInstance = null;
  }
});
</script>

<style scoped>
.map-viewer {
  @apply w-full;
}

.map-container {
  @apply relative w-full overflow-hidden rounded-lg border border-gray-300;
  background-color: #f5f5f5;
}

.map-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 100px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23d1d5db' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'%3E%3C/path%3E%3Ccircle cx='12' cy='10' r='3'%3E%3C/circle%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 48px 48px;
  opacity: 0.3;
  pointer-events: none;
}

.map-container:has(.map-loaded)::after {
  display: none;
}
</style>
