<script setup lang="ts">
import type { FormInstance, Rule } from 'ant-design-vue/es/form';

import type {
  ConservationStatus,
  Species,
  SpeciesCreateRequest,
  SpeciesUpdateRequest,
} from '#/types/species';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { message } from 'ant-design-vue';

import { SpeciesApi } from '#/api/species';
import MapViewer from '#/components/species/MapViewer/index.vue';
import RichTextEditor from '#/components/species/RichTextEditor/index.vue';

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);
const speciesId = computed(() => route.params.id as string);
const isEdit = computed(() => !!speciesId.value);
const speciesData = ref<null | Species>(null);

// 表单数据
const formData = reactive<SpeciesCreateRequest>({
  chineseName: '',
  englishName: '',
  latinName: '',
  taxonomy: {
    kingdom: '',
    phylum: '',
    class: '',
    order: '',
    family: '',
    genus: '',
    species: '',
  },
  conservationStatus: 'NE' as ConservationStatus,
  description: '',
});

// 保护状况选项
const conservationStatusOptions = [
  { value: 'LC', label: '无危 (LC)' },
  { value: 'NT', label: '近危 (NT)' },
  { value: 'VU', label: '易危 (VU)' },
  { value: 'EN', label: '濒危 (EN)' },
  { value: 'CR', label: '极危 (CR)' },
  { value: 'EW', label: '野外灭绝 (EW)' },
  { value: 'EX', label: '灭绝 (EX)' },
  { value: 'DD', label: '数据缺乏 (DD)' },
  { value: 'NE', label: '未评估 (NE)' },
];

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  chineseName: [
    { required: true, message: '请输入物种中文名称', trigger: 'blur' },
    {
      min: 2,
      max: 50,
      message: '中文名称长度应在2-50个字符之间',
      trigger: 'blur',
    },
  ],
  latinName: [
    { required: true, message: '请输入拉丁学名', trigger: 'blur' },
    {
      min: 3,
      max: 100,
      message: '拉丁学名长度应在3-100个字符之间',
      trigger: 'blur',
    },
  ],
  conservationStatus: [
    { required: true, message: '请选择保护状况', trigger: 'change' },
  ],
  'taxonomy.kingdom': [
    { required: true, message: '请输入界', trigger: 'blur' },
  ],
  'taxonomy.phylum': [{ required: true, message: '请输入门', trigger: 'blur' }],
  'taxonomy.class': [{ required: true, message: '请输入纲', trigger: 'blur' }],
  'taxonomy.order': [{ required: true, message: '请输入目', trigger: 'blur' }],
  'taxonomy.family': [{ required: true, message: '请输入科', trigger: 'blur' }],
  'taxonomy.genus': [{ required: true, message: '请输入属', trigger: 'blur' }],
  'taxonomy.species': [
    { required: true, message: '请输入种', trigger: 'blur' },
  ],
};

// 加载物种数据（编辑模式）
const loadSpeciesData = async () => {
  if (!isEdit.value) return;

  try {
    const response = await SpeciesApi.getSpeciesById(speciesId.value);
    if (response.success && response.data) {
      const species = response.data;
      speciesData.value = species;
      Object.assign(formData, {
        chineseName: species.chineseName,
        englishName: species.englishName,
        latinName: species.latinName,
        taxonomy: { ...species.taxonomy },
        conservationStatus: species.conservationStatus,
        description: species.description || '',
      });
    } else {
      message.error(response.message || '加载物种数据失败');
      router.push('/species/list');
    }
  } catch (error) {
    console.error('加载物种数据失败:', error);
    message.error('加载物种数据失败');
    router.push('/species/list');
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;

    let response;
    if (isEdit.value) {
      const updateData: SpeciesUpdateRequest = {
        id: speciesId.value,
        ...formData,
      };
      response = await SpeciesApi.updateSpecies(updateData);
    } else {
      response = await SpeciesApi.createSpecies(formData);
    }

    if (response.success) {
      message.success(isEdit.value ? '物种更新成功' : '物种创建成功');
      router.push('/species/list');
    } else {
      message.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('提交表单失败:', error);
    message.error('操作失败');
  } finally {
    submitting.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  router.push('/species/list');
};

// 地理分布更新处理
const handleDistributionUpdate = (distribution: GeographicDistribution) => {
  if (speciesData.value) {
    speciesData.value.geographicDistribution = distribution;
  }
};

// 地理分布删除处理
const handleDistributionDelete = () => {
  if (speciesData.value) {
    speciesData.value.geographicDistribution = undefined;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadSpeciesData();
});
</script>

<template>
  <div class="species-editor-container">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">
        {{ isEdit ? '编辑物种' : '新增物种' }}
      </h1>
      <p class="mt-1 text-sm text-gray-600">
        {{ isEdit ? '修改物种的基础信息和详细描述' : '创建新的物种记录' }}
      </p>
    </div>

    <!-- 表单内容 -->
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        @finish="handleSubmit"
      >
        <!-- 基础信息 -->
        <div class="mb-8">
          <h3 class="mb-4 text-lg font-medium text-gray-900">基础信息</h3>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="中文名称" name="chineseName">
                <a-input
                  v-model:value="formData.chineseName"
                  placeholder="请输入物种中文名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="英文名称" name="englishName">
                <a-input
                  v-model:value="formData.englishName"
                  placeholder="请输入物种英文名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="拉丁学名" name="latinName">
                <a-input
                  v-model:value="formData.latinName"
                  placeholder="请输入拉丁学名"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 分类信息 -->
        <div class="mb-8">
          <h3 class="mb-4 text-lg font-medium text-gray-900">分类信息</h3>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="界" name="taxonomy.kingdom">
                <a-input
                  v-model:value="formData.taxonomy.kingdom"
                  placeholder="如：动物界"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="门" name="taxonomy.phylum">
                <a-input
                  v-model:value="formData.taxonomy.phylum"
                  placeholder="如：脊索动物门"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="纲" name="taxonomy.class">
                <a-input
                  v-model:value="formData.taxonomy.class"
                  placeholder="如：哺乳纲"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="目" name="taxonomy.order">
                <a-input
                  v-model:value="formData.taxonomy.order"
                  placeholder="如：食肉目"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="科" name="taxonomy.family">
                <a-input
                  v-model:value="formData.taxonomy.family"
                  placeholder="如：猫科"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="属" name="taxonomy.genus">
                <a-input
                  v-model:value="formData.taxonomy.genus"
                  placeholder="如：豹属"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="种" name="taxonomy.species">
                <a-input
                  v-model:value="formData.taxonomy.species"
                  placeholder="如：虎"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保护状况" name="conservationStatus">
                <a-select
                  v-model:value="formData.conservationStatus"
                  placeholder="请选择保护状况"
                >
                  <a-select-option
                    v-for="status in conservationStatusOptions"
                    :key="status.value"
                    :value="status.value"
                  >
                    {{ status.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 详细描述 -->
        <div class="mb-8">
          <h3 class="mb-4 text-lg font-medium text-gray-900">详细描述</h3>
          <a-form-item name="description">
            <RichTextEditor
              v-model="formData.description"
              label="物种详细描述"
              placeholder="请输入物种的详细描述信息，支持富文本格式..."
              :height="400"
              :show-word-count="true"
              :max-length="10000"
            />
          </a-form-item>
          <p class="mt-2 text-sm text-gray-500">
            支持富文本格式，包括标题、列表、链接、图片等多种格式
          </p>
        </div>

        <!-- 地理分布 -->
        <div v-if="isEdit && speciesData" class="mb-8">
          <h3 class="mb-4 text-lg font-medium text-gray-900">地理分布</h3>
          <MapViewer
            :species-id="speciesId"
            :distribution="speciesData.geographicDistribution"
            :height="400"
            @update="handleDistributionUpdate"
            @delete="handleDistributionDelete"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-4">
          <a-button @click="handleCancel"> 取消 </a-button>
          <a-button type="primary" html-type="submit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<style scoped>
.species-editor-container {
  min-height: 100vh;
  padding: 24px;
  background-color: #f5f5f5;
}
</style>
